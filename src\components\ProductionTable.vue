<template>
  <div class="production-table-container h-full flex flex-col">
    <!-- 表格头部 -->
    <div class="table-header border-b border-#0efcff/30">
      <div class="table-row px-4 py-3">
        <div class="table-grid">
          <div class="col-factory font-bold">厂区</div>
          <div class="col-workshop font-bold">车间</div>
          <div class="col-direction font-bold">方位</div>
          <div class="col-spec font-bold">放养规格</div>
          <div class="col-age font-bold">日龄</div>
          <div class="col-count font-bold">头/斤</div>
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="table-body flex-1 overflow-hidden">
      <div
        class="table-content"
        :class="{ 'page-transition': isTransitioning }"
      >
        <div
          v-for="(item, index) in currentPageData"
          :key="`${currentPage}-${index}`"
          class="table-row border-b border-#0efcff/10 px-4 py-3 transition-colors hover:bg-#0efcff/5"
          :class="{ 'bg-#0efcff/3': index % 2 === 0 }"
        >
          <div class="table-grid">
            <div class="col-factory truncate">{{ item.factory }}</div>
            <div class="col-workshop">{{ item.workshop }}</div>
            <div class="col-direction">{{ item.direction }}</div>
            <div class="col-spec">{{ item.specification }}</div>
            <div class="col-age text-#e0f836 font-bold">{{ item.dayAge }}</div>
            <div class="col-count text-#e0f836 font-bold">
              {{ item.countPerJin }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";

interface ProductionItem {
  factory: string;
  workshop: string;
  direction: string;
  specification: string;
  dayAge: number;
  countPerJin: number;
}

interface Props {
  buildingId: number;
}

const props = defineProps<Props>();

const currentPage = ref(0);
const pageSize = 8; // 每页显示8条数据
const isTransitioning = ref(false);
let pageTimer: ReturnType<typeof setInterval>;

// 根据楼栋ID生成生产数据
const generateProductionData = (buildingId: number): ProductionItem[] => {
  const data: ProductionItem[] = [];
  const workshops = ["1", "2", "3", "4", "5", "6"];
  const directions = ["A区", "B区", "C区", "D区"];
  const specifications = ["2到3公分", "3到4公分", "4到5公分", "5到6公分"];

  // 为每个楼栋生成20-30条数据
  const itemCount = 20 + Math.floor(Math.random() * 10);

  for (let i = 0; i < itemCount; i++) {
    data.push({
      factory: `${buildingId}#楼`,
      workshop: workshops[Math.floor(Math.random() * workshops.length)],
      direction: directions[Math.floor(Math.random() * directions.length)],
      specification:
        specifications[Math.floor(Math.random() * specifications.length)],
      dayAge: 30 + Math.floor(Math.random() * 60), // 30-90天
      countPerJin: 80 + Math.floor(Math.random() * 120), // 80-200头/斤
    });
  }

  return data;
};

const productionData = ref<ProductionItem[]>([]);

// 当前页数据
const currentPageData = computed(() => {
  const start = currentPage.value * pageSize;
  const end = start + pageSize;
  return productionData.value.slice(start, end);
});

// 总页数
const totalPages = computed(() => {
  return Math.ceil(productionData.value.length / pageSize);
});

// 自动翻页
const startAutoPage = () => {
  if (pageTimer) {
    clearInterval(pageTimer);
  }

  pageTimer = setInterval(() => {
    if (totalPages.value > 1) {
      // 添加过渡效果
      isTransitioning.value = true;

      setTimeout(() => {
        currentPage.value = (currentPage.value + 1) % totalPages.value;
        isTransitioning.value = false;
      }, 150); // 150ms 过渡时间
    }
  }, 3000); // 每3秒翻页
};

// 监听楼栋变化
watch(
  () => props.buildingId,
  (newBuildingId) => {
    productionData.value = generateProductionData(newBuildingId);
    currentPage.value = 0;
    startAutoPage();
  },
  { immediate: true }
);

onMounted(() => {
  startAutoPage();
});

onUnmounted(() => {
  if (pageTimer) {
    clearInterval(pageTimer);
  }
});
</script>

<style scoped>
.production-table-container {
  font-size: 13px;
  color: #0efcff;
}

.table-row {
  min-height: 52px;
  display: flex;
  align-items: center;
}

.table-header {
  background: rgba(14, 252, 255, 0.15);
  backdrop-filter: blur(5px);
  border-radius: 4px 4px 0 0;
  text-shadow: 0 0 8px rgba(14, 252, 255, 0.6);
}

.table-header .table-row {
  background: transparent;
}

.table-row:hover {
  background: rgba(14, 252, 255, 0.08) !important;
  box-shadow: inset 0 0 20px rgba(14, 252, 255, 0.1);
}

/* 表格网格布局 */
.table-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1.2fr 0.8fr 0.8fr;
  gap: 1rem;
  align-items: center;
  width: 100%;
}

.col-factory,
.col-workshop,
.col-direction,
.col-spec,
.col-age,
.col-count {
  text-align: center;
  justify-self: center;
}

.col-factory {
  white-space: wrap;
}

/* 发光效果 */
.table-header div {
  text-shadow: 0 0 10px rgba(14, 252, 255, 0.8);
}

/* 数字发光 */
.text-#e0f836 {
  text-shadow: 0 0 8px rgba(224, 248, 54, 0.6);
}

/* 表格内容区域 */
.table-body {
  position: relative;
}

.table-content {
  transition: opacity 0.3s ease;
}

/* 页面切换动画 */
.table-content.page-transition {
  opacity: 0;
}
</style>
