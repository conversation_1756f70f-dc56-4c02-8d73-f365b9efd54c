<template>
  <div ref="chartRef" class="relative h-full w-full">
    <div
      v-if="centerText"
      class="pointer-events-none absolute inset-0 z-10 flex items-center justify-center"
    >
      <div class="text-center">
        <div class="mb-1 text-lg text-white font-bold">
          {{ centerText.title }}
        </div>
        <div class="text-2xl text-cyan-400 font-bold">
          {{ centerText.value }}
        </div>
        <div class="text-sm text-gray-300">{{ centerText.unit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import * as echarts from "echarts";
import "echarts-gl";

interface PieData {
  name: string;
  value: number;
  itemStyle?: {
    color?: string;
    opacity?: number;
  };
}

interface CenterText {
  title: string;
  value: string | number;
  unit?: string;
}

interface Props {
  data: PieData[];
  internalDiameterRatio?: number;
  colors?: string[];
  centerText?: CenterText;
  theme?: string;
}

const props = withDefaults(defineProps<Props>(), {
  internalDiameterRatio: 0.75,
  colors: () => [
    "#2B99EE",
    "#EB7A6A",
    "#4FED7D",
    "#47D8E1",
    "#E1CD47",
    "#4F5AED",
    "#E19D47",
  ],
  theme: "dark",
});

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;
let hoveredIndex = "";

// 生成扇形的曲面参数方程
function getParametricEquation(
  startRatio: number,
  endRatio: number,
  isSelected: boolean,
  isHovered: boolean,
  k: number,
  h: number
) {
  // 计算
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== "undefined" ? k : 1 / 3;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x(u: number, v: number) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y(u: number, v: number) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z(u: number, v: number) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      // 当前图形的高度是Z根据h（每个value的值决定的）
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}

// 生成模拟 3D 饼图的配置项
function getPie3D(pieData: PieData[], internalDiameterRatio: number) {
  const series: any[] = [];
  // 总和
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  const legendData: string[] = [];

  const k =
    typeof internalDiameterRatio !== "undefined"
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
      : 1 / 3;

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i += 1) {
    sumValue += pieData[i].value;
    const seriesItem: any = {
      name:
        typeof pieData[i].name === "undefined"
          ? `series${i}`
          : pieData[i].name + "  " + pieData[i].value,
      type: "surface",
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k,
      },
      // 确保可以接收鼠标事件
      silent: false,
      // 添加tooltip配置
      tooltip: {
        show: true,
      },
    };

    if (typeof pieData[i].itemStyle !== "undefined") {
      const { itemStyle } = pieData[i];
      if (typeof pieData[i].itemStyle?.color !== "undefined") {
        itemStyle!.color = pieData[i].itemStyle!.color;
      }
      if (typeof pieData[i].itemStyle?.opacity !== "undefined") {
        itemStyle!.opacity = pieData[i].itemStyle!.opacity;
      }
      seriesItem.itemStyle = itemStyle;
    } else {
      // 如果没有指定颜色，使用默认颜色
      seriesItem.itemStyle = {
        color: props.colors[i % props.colors.length],
      };
    }

    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i += 1) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      // 使除了第一个之外的值都是10
      series[i].pieData.value === series[0].pieData.value ? 35 : 10
    );
    startValue = endValue;
    legendData.push(series[i].name);
  }

  // 准备待返回的配置项，把准备好的 legendData、series 传入。
  const option = {
    backgroundColor: "transparent", // 设置透明背景
    color: props.colors,
    legend: {
      type: "scroll",
      data: legendData,
      icon: "roundRect",
      padding: 5,
      itemGap: 20,
      bottom: 0,
      itemWidth: 10, // 设置宽度
      itemHeight: 10, // 设置高度
      selectedMode: true,
      textStyle: {
        color: "#0efcff",
        fontSize: 12,
        lineHeight: 14,
        rich: {
          a: {
            verticalAlign: "middle",
          },
        },
        padding: [0, 0, -3, 0],
      },
    },
    tooltip: {
      show: true,
      trigger: "item",
      formatter: (params: any) => {
        // 从series名称中提取实际名称和值
        const nameAndValue = params.seriesName.split("  ");
        const name = nameAndValue[0] || params.seriesName;
        const value = nameAndValue[1] || "N/A";

        return `${name}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>数量: ${value}`;
      },
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#0efcff",
      borderWidth: 1,
      textStyle: {
        color: "#0efcff",
        fontSize: 12,
      },
      confine: true,
      extraCssText: "z-index: 9999;",
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: "dataMax",
    },
    grid3D: {
      show: false,
      boxHeight: 16,
      top: "-18%",
      viewControl: {
        // 3d效果可以放大、旋转等，请自己去查看官方配置
        alpha: 27,
        beta: 70, //旋转角度
        rotateSensitivity: 1,
        zoomSensitivity: 0,
        panSensitivity: 0,
        // autoRotate: true,
        distance: 150,
      },
      // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
      postEffect: {
        // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
        enable: false,
        bloom: {
          enable: true,
          bloomIntensity: 0.1,
        },
        SSAO: {
          enable: true,
          quality: "medium",
          radius: 2,
        },
      },
      // 确保可以接收鼠标事件
      silent: false,
    },
    series,
  };

  return option;
}

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value, props.theme);
    const option = getPie3D(props.data, props.internalDiameterRatio);
    chartInstance.setOption(option);

    // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
    // 监听 mouseover，近似实现高亮（放大）效果
    chartInstance.on("mouseover", function (params: any) {
      // 准备重新渲染扇形所需的参数
      let isSelected: boolean;
      let isHovered: boolean;
      let startRatio: number;
      let endRatio: number;
      let k: number;

      const currentOption = chartInstance!.getOption() as any;

      // 如果触发 mouseover 的扇形当前已高亮，则不做操作
      if (hoveredIndex === params.seriesIndex) {
        return;
        // 否则进行高亮及必要的取消高亮操作
      } else {
        // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
        if (hoveredIndex !== "") {
          // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
          isSelected = currentOption.series[hoveredIndex].pieStatus.selected;
          isHovered = false;
          startRatio = currentOption.series[hoveredIndex].pieData.startRatio;
          endRatio = currentOption.series[hoveredIndex].pieData.endRatio;
          k = currentOption.series[hoveredIndex].pieStatus.k;

          // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
          currentOption.series[hoveredIndex].parametricEquation =
            getParametricEquation(
              startRatio,
              endRatio,
              isSelected,
              isHovered,
              k,
              currentOption.series[hoveredIndex].pieData.value
            );

          currentOption.series[hoveredIndex].pieStatus.hovered = isHovered;

          // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
          hoveredIndex = "";
        }

        // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
        if (params.seriesName !== "mouseoutSeries") {
          // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
          isSelected =
            currentOption.series[params.seriesIndex].pieStatus.selected;
          isHovered = true;
          startRatio =
            currentOption.series[params.seriesIndex].pieData.startRatio;
          endRatio = currentOption.series[params.seriesIndex].pieData.endRatio;
          k = currentOption.series[params.seriesIndex].pieStatus.k;

          // 对当前点击的扇形，执行高亮操作（对 option 更新）
          currentOption.series[params.seriesIndex].parametricEquation =
            getParametricEquation(
              startRatio,
              endRatio,
              isSelected,
              isHovered,
              k,
              currentOption.series[params.seriesIndex].pieData.value + 5
            );

          currentOption.series[params.seriesIndex].pieStatus.hovered =
            isHovered;

          // 记录上次高亮的扇形对应的系列号 seriesIndex
          hoveredIndex = params.seriesIndex;
        }

        // 使用更新后的 option，渲染图表
        chartInstance!.setOption(currentOption);
      }
    });

    // 修正取消高亮失败的 bug
    chartInstance.on("globalout", function () {
      if (hoveredIndex !== "") {
        const currentOption = chartInstance!.getOption() as any;
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
        const isSelected =
          currentOption.series[hoveredIndex].pieStatus.selected;
        const isHovered = false;
        const k = currentOption.series[hoveredIndex].pieStatus.k;
        const startRatio =
          currentOption.series[hoveredIndex].pieData.startRatio;
        const endRatio = currentOption.series[hoveredIndex].pieData.endRatio;

        // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
        currentOption.series[hoveredIndex].parametricEquation =
          getParametricEquation(
            startRatio,
            endRatio,
            isSelected,
            isHovered,
            k,
            currentOption.series[hoveredIndex].pieData.value
          );

        currentOption.series[hoveredIndex].pieStatus.hovered = isHovered;

        // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
        hoveredIndex = "";

        // 使用更新后的 option，渲染图表
        chartInstance!.setOption(currentOption);
      }
    });
  }
};

// 销毁图表
const destroyChart = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
};

// 重新渲染图表
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 监听数据变化
watch(
  () => props.data,
  () => {
    if (chartInstance) {
      const option = getPie3D(props.data, props.internalDiameterRatio);
      chartInstance.setOption(option, true);
    }
  },
  { deep: true }
);

// 监听窗口大小变化
const handleResize = () => {
  resizeChart();
};

onMounted(async () => {
  await nextTick();
  initChart();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  destroyChart();
  window.removeEventListener("resize", handleResize);
});

// 暴露方法给父组件
defineExpose({
  getChartInstance: () => chartInstance,
  resize: resizeChart,
});
</script>
